{% extends 'dashboard_app/base_dashboard.html' %}
{% load static %}
{% load i18n %}

{% block title %}Edit Location - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block dashboard_title %}Edit Location{% endblock %}

{% block sidebar_content %}
    {% include 'dashboard_app/includes/provider_sidebar.html' %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/cozywish_design_system.css' %}">
<style>
    .breadcrumb-cw {
        background: transparent;
        padding: 0;
        margin-bottom: 1.5rem;
    }
    
    .breadcrumb-cw .breadcrumb-item {
        color: var(--cw-text-muted);
    }
    
    .breadcrumb-cw .breadcrumb-item.active {
        color: var(--cw-primary);
        font-weight: 500;
    }
    
    .breadcrumb-cw .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: var(--cw-text-muted);
    }
    
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .form-section-title {
        color: var(--cw-primary);
        font-weight: 600;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--cw-accent);
    }
    
    .btn-cw-primary {
        background: var(--cw-primary);
        border-color: var(--cw-primary);
        color: white;
        padding: 0.75rem 2rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-cw-primary:hover {
        background: var(--cw-primary-light);
        border-color: var(--cw-primary-light);
        color: white;
    }
    
    .btn-cw-secondary {
        background: transparent;
        border: 2px solid var(--cw-primary);
        color: var(--cw-primary);
        padding: 0.75rem 2rem;
        font-weight: 500;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-cw-secondary:hover {
        background: var(--cw-primary);
        color: white;
    }
    
    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .address-preview {
        background: var(--cw-accent);
        border: 1px solid var(--cw-primary);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .address-preview h6 {
        color: var(--cw-primary);
        margin-bottom: 0.5rem;
    }
    
    .coordinates-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .coordinates-info small {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block dashboard_content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-cw">
        {% for breadcrumb in breadcrumbs %}
            {% if breadcrumb.url %}
                <li class="breadcrumb-item">
                    <a href="{{ breadcrumb.url }}" class="text-decoration-none">{{ breadcrumb.name }}</a>
                </li>
            {% else %}
                <li class="breadcrumb-item active" aria-current="page">{{ breadcrumb.name }}</li>
            {% endif %}
        {% endfor %}
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">Edit Location</h2>
        <p class="text-muted mb-0">Update your venue's address and location details</p>
    </div>
</div>

<!-- Edit Form -->
<form method="post" novalidate>
    {% csrf_token %}
    
    <!-- Address Details Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-map-marker-alt me-2"></i>Address Details
        </h4>
        
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="{{ form.street_number.id_for_label }}" class="form-label fw-bold">
                    Street Number <span class="text-danger">*</span>
                </label>
                {{ form.street_number }}
                {% if form.street_number.errors %}
                    {% for error in form.street_number.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Building or house number
                </small>
            </div>
            
            <div class="col-md-9 mb-3">
                <label for="{{ form.street_name.id_for_label }}" class="form-label fw-bold">
                    Street Name <span class="text-danger">*</span>
                </label>
                {{ form.street_name }}
                {% if form.street_name.errors %}
                    {% for error in form.street_name.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    Street name or address line
                </small>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="{{ form.city.id_for_label }}" class="form-label fw-bold">
                    City <span class="text-danger">*</span>
                </label>
                {{ form.city }}
                {% if form.city.errors %}
                    {% for error in form.city.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    City where your venue is located
                </small>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="{{ form.county.id_for_label }}" class="form-label fw-bold">
                    County <span class="text-danger">*</span>
                </label>
                {{ form.county }}
                {% if form.county.errors %}
                    {% for error in form.county.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    County or administrative area
                </small>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="{{ form.state.id_for_label }}" class="form-label fw-bold">
                    State <span class="text-danger">*</span>
                </label>
                {{ form.state }}
                {% if form.state.errors %}
                    {% for error in form.state.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <small class="form-text text-muted">
                    State or province
                </small>
            </div>
        </div>
        
        <!-- Address Preview -->
        <div class="address-preview" id="address-preview">
            <h6><i class="fas fa-eye me-2"></i>Address Preview</h6>
            <p class="mb-0" id="preview-text">
                {% if venue.get_full_address %}
                    {{ venue.get_full_address }}
                {% else %}
                    Complete the address fields above to see preview
                {% endif %}
            </p>
        </div>
    </div>
    
    <!-- Coordinates Section -->
    <div class="form-section">
        <h4 class="form-section-title">
            <i class="fas fa-crosshairs me-2"></i>Coordinates
        </h4>
        
        <div class="coordinates-info">
            <p class="mb-2">
                <i class="fas fa-info-circle me-2"></i>
                Coordinates are automatically determined from your address and help customers find your venue on maps.
            </p>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.latitude.id_for_label }}" class="form-label fw-bold">
                        Latitude
                    </label>
                    {{ form.latitude }}
                    {% if form.latitude.errors %}
                        {% for error in form.latitude.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Auto-filled from address
                    </small>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.longitude.id_for_label }}" class="form-label fw-bold">
                        Longitude
                    </label>
                    {{ form.longitude }}
                    {% if form.longitude.errors %}
                        {% for error in form.longitude.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <small class="form-text text-muted">
                        Auto-filled from address
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Actions -->
    <div class="d-flex flex-column flex-md-row gap-3 justify-content-end">
        <a href="{% url 'venues_app:provider_venue_detail' venue_id=venue.id %}" class="btn btn-cw-secondary">
            <i class="fas fa-times me-2"></i>Cancel
        </a>
        <button type="submit" class="btn btn-cw-primary">
            <i class="fas fa-save me-2"></i>Save Changes
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Address preview update
    function updateAddressPreview() {
        const streetNumber = document.getElementById('{{ form.street_number.id_for_label }}').value.trim();
        const streetName = document.getElementById('{{ form.street_name.id_for_label }}').value.trim();
        const city = document.getElementById('{{ form.city.id_for_label }}').value.trim();
        const county = document.getElementById('{{ form.county.id_for_label }}').value.trim();
        const state = document.getElementById('{{ form.state.id_for_label }}').value.trim();
        
        const previewText = document.getElementById('preview-text');
        
        if (streetNumber || streetName || city || county || state) {
            const addressParts = [];
            if (streetNumber && streetName) {
                addressParts.push(`${streetNumber} ${streetName}`);
            } else if (streetName) {
                addressParts.push(streetName);
            }
            if (city) addressParts.push(city);
            if (county) addressParts.push(county);
            if (state) addressParts.push(state);
            
            previewText.textContent = addressParts.join(', ') || 'Complete the address fields above to see preview';
        } else {
            previewText.textContent = 'Complete the address fields above to see preview';
        }
    }
    
    // Add event listeners to address fields
    const addressFields = [
        '{{ form.street_number.id_for_label }}',
        '{{ form.street_name.id_for_label }}',
        '{{ form.city.id_for_label }}',
        '{{ form.county.id_for_label }}',
        '{{ form.state.id_for_label }}'
    ];
    
    addressFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', updateAddressPreview);
            field.addEventListener('change', updateAddressPreview);
        }
    });
    
    // Initial preview update
    updateAddressPreview();
});
</script>
{% endblock %}
